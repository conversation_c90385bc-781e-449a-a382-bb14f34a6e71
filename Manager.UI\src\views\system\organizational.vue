<template>
	<div class="org-list-container">
		<div class="org-list-content">
			<org-edit @search="search"></org-edit>
			<community-selector @success="handleCommunitySuccess"></community-selector>
			<org-detail @success="handleDetailSuccess"></org-detail>
			<div class="card card--search search-flex">
				<div class="search-left">
					<el-input v-model="searchModel.orgName" placeholder="组织名称" clearable style="width: 200px; margin-right: 16px;" />
					<el-button type="primary" @click="search">搜索</el-button>
				</div>
				<div class="search-right">
					<el-button type="primary" @click="add(0)">添加</el-button>
				</div>
			</div>
			<div class="card card--table">
				<div class="table-col">
					<el-table :data="orgList" row-key="id" style="width: 100%; height: 100%;" class="data-table"
						:tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
						<el-table-column prop="orgName" header-align="center" label="组织名称" min-width="100" />
						<el-table-column prop="sort" header-align="center" align="center" label="排序" width="140" />
						<el-table-column prop="createTime" header-align="center" align="center" label="创建时间" width="200">
							<template #default="scope">
								{{ formatDate(scope.row.createTime) }}
							</template>
						</el-table-column>
						<el-table-column prop="updateTime" header-align="center" align="center" label="更新时间" width="200">
							<template #default="scope">
								{{ formatDate(scope.row.updateTime) }}
							</template>
						</el-table-column>
						<el-table-column header-align="center" align="center" label="操作" width="340" fixed="right">
							<template #default="scope">
								<el-button type="text" size="mini" @click="add(scope.row.id)">添加子级</el-button>
								<el-button type="text" size="mini" @click="edit(scope.row.id)">编辑</el-button>
								<el-button type="text" size="mini" @click="deleted(scope.row.id)">删除</el-button>
								<el-button type="text" size="mini" @click="relevCommunity(scope.row.id)">关联小区</el-button>
								<el-button type="text" size="mini" @click="showDetail(scope.row.id)">详情</el-button>
							</template>
						</el-table-column>
					</el-table>
				</div>
				<div class="pagination-col">
					<el-pagination background layout="prev, pager, next" @current-change="currentChange" @prev-click="prevClick"
						@next-click="nextClick" :total="total"></el-pagination>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { listOrg, deleteOrg, getOrg } from "@/api/system/org"
import mitt from "@/utils/mitt";
import orgEdit from "@/components/system/orgEdit.vue"
import communitySelector from "@/components/system/communitySelector.vue"
import orgDetail from "@/components/system/orgDetail.vue"

export default {
	components: { orgEdit, communitySelector, orgDetail },
	data() {
		return {
			searchModel: {
				pageNum: 1,
				pageSize: 10
			},
			orgList: [],
			total: 0
		}
	},
	methods: {
		search() {
			listOrg(this.searchModel)
				.then(res => {
					this.orgList = res.data.data.list
					this.total = res.data.data.total
				}).catch(err => {
					this.$message.error(err.data.errorMessage)
				})
		},
		edit(id) {
			getOrg(id)
				.then(res => {
					mitt.emit('openOrgEdit', res.data.data)
				})
				.catch(err => {
					this.$message.error(err.data.errorMessage)
				})
		},
		add(parentId) {
			mitt.emit('openOrgAdd', parentId)
		},
		deleted(id) {
			this.$confirm('删除组织, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				deleteOrg(id)
					.then(() => {
						this.search()
						this.$message.success("操作成功")
					}).catch((err) => {
						this.$message.error(err.data.errorMessage)
					})
			}).catch(() => { })
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick() {
			if (this.searchModel.pageNum > 1) {
				this.searchModel.pageNum--
				this.search()
			}
		},
		nextClick() {
			this.searchModel.pageNum++
			this.search()
		},
		formatDate(dateString) {
			if (!dateString) return ''
			const date = new Date(dateString)
			return date.toLocaleString('zh-CN', {
				year: 'numeric',
				month: '2-digit',
				day: '2-digit',
				hour: '2-digit',
				minute: '2-digit',
				second: '2-digit'
			})
		},
		// 关联小区
		relevCommunity(orgId) {
			mitt.emit('openCommunitySelector', orgId)
		},
		// 查看详情
		showDetail(orgId) {
			mitt.emit('openOrgDetail', orgId)
		},
		// 小区关联成功回调
		handleCommunitySuccess() {
			this.$message.success('小区关联成功')
			// 可以在这里刷新数据或执行其他操作
		},
		// 详情保存成功回调
		handleDetailSuccess() {
			this.search() // 刷新组织列表
		},
		async init() {
			mitt.off('openOrgEdit')
			mitt.off('openOrgAdd')
			try {
				const org_res = await listOrg(this.searchModel)
				this.orgList = org_res.data.data.list
				this.total = org_res.data.data.total
			} catch (err) {
				this.$message.error(err.data.errorMessage)
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
.org-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.org-list-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.data-table {
  flex: 1;
  height: 100%;
}

.pagination-col {
  padding: 16px 0;
  text-align: center;
  flex-shrink: 0;
}

.search-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
}

.search-left {
  display: flex;
  align-items: center;
}

.search-right {
  display: flex;
  align-items: center;
}


.card--search {
  margin-bottom: 20px;
  flex: none;
  height: auto;
  padding: 20px 20px;
  display: flex;
  align-items: center;
}

.card--table {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  margin-top: 0;
}
</style>