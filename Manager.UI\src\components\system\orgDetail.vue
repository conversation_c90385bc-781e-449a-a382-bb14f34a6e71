<template>
  <el-dialog 
    width="800px" 
    v-loading="loading" 
    destroy-on-close 
    v-model="dialog.show" 
    :title="dialog.title"
  >
    <el-form 
      :rules="rules" 
      ref="formRef" 
      :model="orgDetailModel" 
      label-width="120px"
      class="org-detail-form"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">基本信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="组织编码" prop="orgCode">
              <el-input v-model="orgDetailModel.orgCode" placeholder="请输入组织编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input-number 
                v-model="orgDetailModel.sort" 
                placeholder="排序" 
                :min="0" 
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="地址" prop="address">
              <el-input v-model="orgDetailModel.address" placeholder="请输入地址" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="note">
              <el-input 
                v-model="orgDetailModel.note" 
                type="textarea" 
                placeholder="请输入备注" 
                :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 法人信息 -->
      <div class="form-section">
        <div class="section-title">法人信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="法人姓名" prop="legalPersonName">
              <el-input v-model="orgDetailModel.legalPersonName" placeholder="请输入法人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="法人电话" prop="legalPersonPhone">
              <el-input v-model="orgDetailModel.legalPersonPhone" placeholder="请输入法人电话" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 联系信息 -->
      <div class="form-section">
        <div class="section-title">联系信息</div>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="orgDetailModel.email" placeholder="请输入邮箱地址" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 媒体文件 -->
      <div class="form-section">
        <div class="section-title">媒体文件</div>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="媒体文件" prop="media">
              <el-upload
                class="media-uploader"
                :action="uploadUrl"
                :show-file-list="true"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                :before-upload="beforeUpload"
                multiple
                accept="image/*,video/*"
              >
                <el-button type="primary">选择文件</el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持上传图片和视频文件，单个文件不超过50MB
                  </div>
                </template>
              </el-upload>
              <div v-if="mediaList.length > 0" class="media-preview">
                <div v-for="(item, index) in mediaList" :key="index" class="media-item">
                  <img v-if="isImage(item)" :src="getMediaUrl(item)" class="preview-image" />
                  <video v-else :src="getMediaUrl(item)" class="preview-video" controls></video>
                  <el-button 
                    type="danger" 
                    size="small" 
                    @click="removeMedia(index)"
                    class="remove-btn"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 扩展数据 -->
      <div class="form-section">
        <div class="section-title">扩展数据</div>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="扩展数据" prop="extentData">
              <el-input 
                v-model="orgDetailModel.extentData" 
                type="textarea" 
                placeholder="请输入扩展数据（JSON格式）" 
                :rows="4"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 时间信息（只读） -->
      <div class="form-section" v-if="orgDetailModel.id">
        <div class="section-title">时间信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="创建时间">
              <el-input :value="formatDate(orgDetailModel.createTime)" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="更新时间">
              <el-input :value="formatDate(orgDetailModel.updateTime)" readonly />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="onSubmit" :loading="loading">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { getOrgDetail, updateOrgDetail, addOrgDetail } from '@/api/system/org'
import mitt from '@/utils/mitt'

export default {
  name: 'OrgDetail',
  data() {
    return {
      loading: false,
      dialog: {
        show: false,
        title: '组织详情'
      },
      orgDetailModel: {
        id: null,
        orgCode: '',
        address: '',
        note: '',
        legalPersonName: '',
        legalPersonPhone: '',
        email: '',
        media: '',
        sort: 0,
        orgId: null,
        extentData: ''
      },
      mediaList: [],
      uploadUrl: import.meta.env.VITE_BASE_API + "/common-api/v1/file/upload",
      imgServer: import.meta.env.VITE_BASE_API + "/common-api/v1/file/",
      rules: {
        orgCode: [
          { required: true, message: '请输入组织编码', trigger: 'blur' }
        ],
        legalPersonPhone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 提交表单
    onSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (!valid) return

        this.loading = true
        
        // 处理媒体文件
        this.orgDetailModel.media = this.mediaList.join(',')
        
        const apiCall = this.orgDetailModel.id ? 
          updateOrgDetail(this.orgDetailModel) : 
          addOrgDetail(this.orgDetailModel)

        apiCall
          .then(() => {
            this.$message.success(this.orgDetailModel.id ? '修改成功' : '添加成功')
            this.dialog.show = false
            this.$emit('success')
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '操作失败')
          })
          .finally(() => {
            this.loading = false
          })
      })
    },

    // 文件上传成功
    handleUploadSuccess(response) {
      if (response.code === 200) {
        this.mediaList.push(response.data)
        this.$message.success('文件上传成功')
      } else {
        this.$message.error(response.message || '文件上传失败')
      }
    },

    // 文件上传失败
    handleUploadError() {
      this.$message.error('文件上传失败')
    },

    // 上传前检查
    beforeUpload(file) {
      const isValidSize = file.size / 1024 / 1024 < 50
      if (!isValidSize) {
        this.$message.error('文件大小不能超过50MB')
      }
      return isValidSize
    },

    // 移除媒体文件
    removeMedia(index) {
      this.mediaList.splice(index, 1)
    },

    // 判断是否为图片
    isImage(filename) {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
      return imageExtensions.some(ext => filename.toLowerCase().endsWith(ext))
    },

    // 获取媒体文件URL
    getMediaUrl(filename) {
      return process.env.VUE_APP_IMG_SERVER + filename
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 重置表单
    resetForm() {
      this.orgDetailModel = {
        id: null,
        orgCode: '',
        address: '',
        note: '',
        legalPersonName: '',
        legalPersonPhone: '',
        email: '',
        media: '',
        sort: 0,
        orgId: null,
        extentData: ''
      }
      this.mediaList = []
      if (this.$refs.formRef) {
        this.$refs.formRef.clearValidate()
      }
    }
  },

  mounted() {
    // 监听打开组织详情事件
    mitt.on('openOrgDetail', (orgId) => {
      this.resetForm()
      this.orgDetailModel.orgId = orgId
      this.dialog.show = true
      
      if (orgId) {
        this.loading = true
        getOrgDetail(orgId)
          .then(res => {
            if (res.data.data) {
              this.orgDetailModel = { ...res.data.data }
              // 处理媒体文件
              if (this.orgDetailModel.media) {
                this.mediaList = this.orgDetailModel.media.split(',').filter(item => item.trim())
              }
            }
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '获取组织详情失败')
          })
          .finally(() => {
            this.loading = false
          })
      }
    })
  },

  beforeUnmount() {
    mitt.off('openOrgDetail')
  }
}
</script>

<style scoped>
.org-detail-form {
  padding: 0;
}

.form-section {
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.form-section:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.section-title {
  font-weight: 600;
  margin-bottom: 20px;
  color: #495057;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e9ecef;
}

.section-title::before {
  content: "";
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

/* 表单项样式 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #495057;
}

:deep(.el-input__inner) {
  border-radius: 8px;
  border: 1px solid #ced4da;
  transition: all 0.3s ease;
}

:deep(.el-input__inner:focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
  border: 1px solid #ced4da;
  transition: all 0.3s ease;
}

:deep(.el-textarea__inner:focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

/* 文件上传样式 */
.media-uploader {
  width: 100%;
}

:deep(.el-upload) {
  border: 2px dashed #ced4da;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  padding: 20px;
  text-align: center;
  background: #f8f9fa;
}

:deep(.el-upload:hover) {
  border-color: #667eea;
  background: #f0f2ff;
}

:deep(.el-upload .el-button) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-weight: 500;
  color: white;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

:deep(.el-upload .el-button:hover) {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
}

:deep(.el-upload__tip) {
  color: #6c757d;
  font-size: 12px;
  margin-top: 8px;
}

/* 媒体预览样式 */
.media-preview {
  margin-top: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 16px;
}

.media-item {
  position: relative;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.media-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.preview-image,
.preview-video {
  width: 100%;
  height: 100px;
  object-fit: cover;
  display: block;
}

.remove-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  background: rgba(220, 53, 69, 0.9);
  color: white;
  border: none;
  opacity: 0;
  transition: all 0.3s ease;
}

.media-item:hover .remove-btn {
  opacity: 1;
}

.remove-btn:hover {
  background: #dc3545;
  transform: scale(1.05);
}

/* 弹窗样式 */
:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  margin: 0;
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

:deep(.el-dialog__close) {
  color: white;
  font-size: 20px;
}

:deep(.el-dialog__close:hover) {
  color: #f0f0f0;
}

:deep(.el-dialog__body) {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-dialog__body::-webkit-scrollbar) {
  width: 6px;
}

:deep(.el-dialog__body::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 3px;
}

:deep(.el-dialog__body::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 3px;
}

:deep(.el-dialog__body::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}

/* 底部按钮样式 */
.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
  margin-top: 20px;
}

.dialog-footer .el-button {
  padding: 10px 24px;
  font-weight: 500;
  border-radius: 6px;
  margin-left: 12px;
}

.dialog-footer .el-button--primary {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.dialog-footer .el-button--primary:hover {
  background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

.dialog-footer .el-button--primary.is-loading {
  background: #6c757d;
  box-shadow: none;
}

/* 只读字段样式 */
:deep(.el-input.is-disabled .el-input__inner) {
  background-color: #f8f9fa;
  border-color: #e9ecef;
  color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-section {
    padding: 16px;
    margin-bottom: 16px;
  }

  .section-title {
    font-size: 14px;
  }

  .media-preview {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
  }

  .preview-image,
  .preview-video {
    height: 80px;
  }

  :deep(.el-dialog__body) {
    padding: 16px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-section {
  animation: fadeInUp 0.3s ease-out;
}

.form-section:nth-child(1) { animation-delay: 0.1s; }
.form-section:nth-child(2) { animation-delay: 0.2s; }
.form-section:nth-child(3) { animation-delay: 0.3s; }
.form-section:nth-child(4) { animation-delay: 0.4s; }
.form-section:nth-child(5) { animation-delay: 0.5s; }
</style>
