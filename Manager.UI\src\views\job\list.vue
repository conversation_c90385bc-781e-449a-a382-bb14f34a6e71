<template>
  <div class="job-list-container">
    <div class="job-list-content">
      <!-- 搜索栏 -->
      <div class="card card--search search-flex">
        <div class="search-left">
          <el-input
            v-model="searchModel.jobName"
            placeholder="请输入任务名称"
            clearable
            style="width: 200px; margin-right: 16px;"
          />
          <el-select
            v-model="searchModel.jobGroup"
            placeholder="请选择任务组"
            clearable
            style="width: 150px; margin-right: 16px;"
          >
            <el-option
              v-for="group in jobGroupOptions"
              :key="group.value"
              :label="group.label"
              :value="group.value"
            />
          </el-select>
          <el-select
            v-model="searchModel.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px; margin-right: 16px;"
          >
            <el-option
              v-for="status in statusOptions"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
          <el-button type="primary" @click="search">搜索</el-button>
          <el-button @click="resetSearch" style="margin-left: 8px;">重置</el-button>
        </div>
        <div class="search-right">
          <el-button type="primary" @click="openJobEdit()">新增任务</el-button>
        </div>
      </div>

      <!-- 表格 -->
      <div class="card card--table">
        <div class="table-col">
          <el-table
            :data="jobList"
            v-loading="loading"
            style="width: 100%; height: 100%;"
            class="data-table"
          >
            <el-table-column prop="id" label="任务ID" width="80" align="center" />
            <el-table-column prop="jobName" label="任务名称" min-width="150" show-overflow-tooltip />
            <el-table-column prop="jobGroup" label="任务组" width="120" align="center">
              <template #default="scope">
                {{ getJobGroupLabel(scope.row.jobGroup) }}
              </template>
            </el-table-column>
            <el-table-column prop="invokeTarget" label="调用目标" min-width="200" show-overflow-tooltip />
            <el-table-column prop="cronExpression" label="Cron表达式" width="150" show-overflow-tooltip />
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getStatusTagType(scope.row.status)">
                  {{ getStatusLabel(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="160" align="center">
              <template #default="scope">
                {{ formatDate(scope.row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="320" fixed="right" align="center">
              <template #default="scope">
                <el-button
                  type="text"
                  size="mini"
                  @click="openJobDetail(scope.row)"
                >
                  详情
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  @click="openJobEdit(scope.row)"
                >
                  编辑
                </el-button>
                <el-button
                  v-if="scope.row.status === 'stop'"
                  type="text"
                  size="mini"
                  @click="handleStart(scope.row)"
                >
                  启动
                </el-button>
                <el-button
                  v-if="scope.row.status === 'run'"
                  type="text"
                  size="mini"
                  @click="handleStop(scope.row)"
                >
                  停止
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  @click="handleRun(scope.row)"
                >
                  执行
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  @click="handleDelete(scope.row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-col">
          <el-pagination
            background
            layout="prev, pager, next"
            @current-change="handleCurrentChange"
            @prev-click="prevClick"
            @next-click="nextClick"
            :total="total"
          />
        </div>
      </div>
    </div>

    <!-- 编辑对话框 -->
    <JobEdit ref="jobEditRef" @refresh="search" />

    <!-- 详情对话框 -->
    <JobDetail ref="jobDetailRef" />
  </div>
</template>

<script>
import {
  listJob,
  deleteJob,
  startJob,
  stopJob,
  runJob,
  JOB_STATUS,
  MISFIRE_POLICY,
  CONCURRENT
} from '@/api/job'
import { listDictByNameEn } from '@/api/system/dict'
import JobEdit from './components/JobEdit.vue'
import JobDetail from './components/JobDetail.vue'

export default {
  name: 'JobList',
  components: {
    JobEdit,
    JobDetail
  },
  data() {
    return {
      jobList: [],
      loading: false,
      total: 0,
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        jobName: '',
        jobGroup: '',
        status: ''
      },
      // 字典数据
      statusOptions: [],
      jobGroupOptions: []
    }
  },

  created() {
    this.search()
    this.initDictData()
  },

  methods: {
    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        // 加载任务状态字典
        const statusRes = await listDictByNameEn('job_status')
        this.statusOptions = (statusRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 加载任务组字典
        const groupRes = await listDictByNameEn('jobGroups')
        this.jobGroupOptions = (groupRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))
      } catch (err) {
        console.error('加载字典数据失败:', err)
        // 使用默认数据
        this.statusOptions = JOB_STATUS
        this.jobGroupOptions = [
          { value: 'default', label: '默认组' },
          { value: 'system', label: '系统组' },
          { value: 'business', label: '业务组' }
        ]
      }
    },

    /**
     * 搜索任务列表
     */
    search() {
      this.loading = true
      const params = { ...this.searchModel }
      
      // 清理空值参数
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key]
        }
      })

      listJob(params)
        .then(res => {
          this.jobList = res.data.data.list || []
          this.total = res.data.data.total || 0
        })
        .catch(err => {
          this.$message.error(err.data?.errorMessage || '查询失败')
          this.jobList = []
          this.total = 0
        })
        .finally(() => {
          this.loading = false
        })
    },

    /**
     * 重置搜索条件
     */
    resetSearch() {
      this.searchModel = {
        pageNum: 1,
        pageSize: 10,
        jobName: '',
        jobGroup: '',
        status: ''
      }
      this.search()
    },

    /**
     * 当前页变化
     */
    handleCurrentChange(page) {
      this.searchModel.pageNum = page
      this.search()
    },

    /**
     * 上一页点击
     */
    prevClick() {
      if (this.searchModel.pageNum > 1) {
        this.searchModel.pageNum--
        this.search()
      }
    },

    /**
     * 下一页点击
     */
    nextClick() {
      this.searchModel.pageNum++
      this.search()
    },

    /**
     * 格式化日期
     */
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    /**
     * 打开任务编辑对话框
     */
    openJobEdit(job = null) {
      this.$refs.jobEditRef.open(job)
    },

    /**
     * 打开任务详情对话框
     */
    openJobDetail(job) {
      this.$refs.jobDetailRef.open(job)
    },

    /**
     * 启动任务
     */
    handleStart(job) {
      this.$confirm(`确定要启动任务"${job.jobName}"吗？`, '启动确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        startJob(job.id)
          .then(() => {
            this.$message.success('启动成功')
            this.search()
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '启动失败')
          })
      })
    },

    /**
     * 停止任务
     */
    handleStop(job) {
      this.$confirm(`确定要停止任务"${job.jobName}"吗？`, '停止确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        stopJob(job.id)
          .then(() => {
            this.$message.success('停止成功')
            this.search()
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '停止失败')
          })
      })
    },

    /**
     * 立即执行任务
     */
    handleRun(job) {
      this.$confirm(`确定要立即执行任务"${job.jobName}"吗？`, '执行确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        runJob(job.id)
          .then(() => {
            this.$message.success('执行成功')
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '执行失败')
          })
      })
    },

    /**
     * 删除任务
     */
    handleDelete(job) {
      this.$confirm(`确定要删除任务"${job.jobName}"吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteJob(job.id)
          .then(() => {
            this.$message.success('删除成功')
            this.search()
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '删除失败')
          })
      })
    },

    /**
     * 获取任务组标签文本
     */
    getJobGroupLabel(group) {
      const item = this.jobGroupOptions.find(item => item.value === group)
      return item ? item.label : group
    },

    /**
     * 获取状态标签类型
     */
    getStatusTagType(status) {
      const tagMap = {
        'run': 'success',
        'stop': 'danger'
      }
      return tagMap[status] || ''
    },

    /**
     * 获取状态标签文本
     */
    getStatusLabel(status) {
      const item = this.statusOptions.find(item => item.value === status)
      return item ? item.label : status
    }
  }
}
</script>

<style scoped>
.job-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.job-list-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.data-table {
  flex: 1;
  height: 100%;
}

.pagination-col {
  padding: 16px 0;
  text-align: center;
  flex-shrink: 0;
}

.search-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
}

.search-left {
  display: flex;
  align-items: center;
}

.search-right {
  display: flex;
  align-items: center;
}

.card--search {
  margin-bottom: 20px;
  flex: none;
  height: auto;
  padding: 20px 20px;
  display: flex;
  align-items: center;
}

.card--table {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  margin-top: 0;
}
</style>
