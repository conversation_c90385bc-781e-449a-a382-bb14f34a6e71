<template>
	<el-dialog width="600px" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="orgModel" label-width="120px">
			<el-row>
				<el-col :span="20">
					<el-form-item label="组织名称" prop="orgName">
						<el-input v-model="orgModel.orgName" placeholder="组织名称"></el-input>
					</el-form-item>
				</el-col>
			
			</el-row>
			<el-row>
			
				<el-col :span="20">
					<el-form-item label="排序" prop="sort">
						<el-input-number v-model="orgModel.sort" placeholder="排序" :min="0" style="width: 100%;"></el-input-number>
					</el-form-item>
				</el-col>
			</el-row>


			<el-row>
				<el-col :span="20">
					<el-form-item label="备注" prop="note">
						<el-input v-model="orgModel.note" type="textarea" placeholder="备注" :rows="3"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialog.show = false">取 消</el-button>
				<el-button type="primary" @click="onSubmit">确 定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script>
import { addOrg, editOrg } from "@/api/system/org";
import mitt from "@/utils/mitt";

export default {
	data() {
		return {
			loading: false,
			orgModel: {},
			dialog: {},
			rules: {
				orgName: [{
					required: true,
					message: '请输入组织名称',
					trigger: 'blur',
				}],
				sort: [{
					type: 'number',
					message: '排序必须为数字',
					trigger: 'blur',
				}]
			}
		}
	},
	methods: {
		onSubmit() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					this.loading = true
					if (this.orgModel.id == 0 || !this.orgModel.id) {
						// 添加组织
						addOrg(this.orgModel)
							.then(res => {
								this.$message.success("操作成功")
								this.$emit("search")
								this.dialog.show = false
							})
							.catch(err => {
								this.$message.error(err.data.errorMessage);
							})
							.finally(() => {
								this.loading = false
							})
					} else {
						// 编辑组织
						editOrg(this.orgModel)
							.then(res => {
								this.$message.success("操作成功")
								this.$emit("search")
								this.dialog.show = false
							})
							.catch(err => {
								this.$message.error(err.data.errorMessage);
							})
							.finally(() => {
								this.loading = false
							})
					}
				}
			})
		}
	},
	mounted() {
		mitt.on('openOrgEdit', (org) => {
			this.orgModel = { ...org }
			this.dialog.show = true
			this.dialog.title = "修改组织信息"
		})
		mitt.on('openOrgAdd', (parentId) => {
			this.orgModel = {
				id: 0,
				parentId: parentId || 0,
				sort: 0
			}
			this.dialog.show = true
			this.dialog.title = "添加组织"
		})
	},
	beforeUnmount() {
		mitt.off('openOrgEdit')
		mitt.off('openOrgAdd')
	}
}
</script>

<style scoped>
.dialog-footer {
	text-align: right;
}
</style>
