import request from '@/utils/request'

export const authLogin = (data) =>

	request({
		url: '/manage-api/v1/auth/token',
		method: 'post',
		data: data
	})
export const authLoginOut = () =>

	request({
		url: '/manage-api/v1/auth/logout',
		method: 'post'
	})
export const getVerifyCode = () =>

	request({
		url: '/manage-api/v1/auth/verify-code',
		method: 'get'
	}) 

export const refreshToken= (params) =>

	request({
		url: '/manage-api/v1/auth/refresh?refreshToken',
		method: 'post'
	})