<template>
	<div class="person-list-container">
		<person-edit ref="personEdit" :certificateList="certificateList" :statusList="statusList" @search="search"></person-edit>
		<div class="card card--search search-flex">
			<el-input v-model="searchModel.personName" placeholder="人员姓名" clearable style="width: 200px; margin-right: 16px;" />
			<el-input v-model="searchModel.phone" placeholder="手机号" clearable style="width: 200px; margin-right: 16px;" />
			<el-input v-model="searchModel.personNumber" placeholder="员工编号" clearable style="width: 200px; margin-right: 16px;" />
			<el-select v-model="searchModel.status" placeholder="状态" clearable style="width: 200px; margin-right: 16px;">
				<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn" :value="item.nameEn" />
			</el-select>
			<el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
			<el-button type="primary" @click="add">添加</el-button>
		</div>
		<div class="card card--table">
			<div class="table-col">
				<el-table stripe :data="personList" style="width: 100%; height: 100%;" class="data-table">
					<el-table-column prop="id" align="center" label="ID" width="80" />
					<el-table-column prop="personName" align="center" label="姓名" width="120" />
					<el-table-column prop="personNumber" align="center" label="员工编号" width="120" />
					<el-table-column prop="phone" align="center" label="手机号" width="130" />
					<el-table-column prop="email" align="center" label="邮箱" width="180" />
					<el-table-column prop="entryTime" align="center" label="入职时间" width="120" />
					<el-table-column prop="status" align="center" label="状态" width="100">
						<template #default="scope">
							<el-tag :type="getStatusType(scope.row.status)">
								{{ formatStatus(scope.row.status) }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="salary" align="center" label="薪资" width="100" />
					<el-table-column prop="createTime" align="center" label="创建时间" width="160" />
					<el-table-column align="center" label="操作" width="200">
						<template #default="scope">
							<el-button type="primary" size="small" @click="edit(scope.row.id)">编辑</el-button>
							<el-button type="danger" size="small" @click="deleted(scope.row.id)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<div class="pagination-container">
				<el-pagination v-model:current-page="searchModel.pageNum" v-model:page-size="searchModel.pageSize"
					:page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
					@size-change="search" @current-change="currentChange" />
			</div>
		</div>
	</div>
</template>
<script>
import { listPerson, deletePerson, getPerson } from "@/api/system/person"
import { listDictByNameEn } from "@/api/system/dict"
import mitt from "@/utils/mitt"
import personEdit from "@/components/system/personEdit.vue"
export default {
	components: { personEdit  },
	data() {
		return {
			searchModel: {
				pageNum: 1,
				pageSize: 10,
				personName: '',
				phone: '',
				personNumber: '',
				status: ''
			},
			personList: [],
            certificateList: [],
			statusList: [],
			total: 0
		}
	},
	methods: {
		search() {
			listPerson(this.searchModel)
				.then(res => {
					this.personList = res.data.data.list
					this.total = res.data.data.total
				})
				.catch(err => {
					this.$message.error(err.data.errorMessage)
				})
		},
		add() {
			mitt.emit('openPersonAdd');
		},
		edit(id) {
			getPerson(id)
				.then(res => {
					mitt.emit('openPersonEdit', res.data.data)
				})
				.catch(err => {
					this.$message.error(err.data.errorMessage)
				})
		},
		deleted(id) {
			this.$confirm('删除员工, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				deletePerson(id)
					.then(() => {
						this.search()
						this.$message.success("操作成功")
					})
					.catch(err => {
						this.$message.error(err.data.errorMessage)
					})
			}).catch(() => { })
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		formatStatus(status) {
			const statusItem = this.statusList.find(item => item.nameEn === status)
			return statusItem ? statusItem.nameCn : status
		},
		getStatusType(status) {
			// 根据状态返回对应的标签类型
			const statusMap = {
				'normal': 'success',
				'inactive': 'danger',
				'trial': 'warning'
			}
			return statusMap[status] || 'info'
		},
		async init() {
			try {
				const [ certificate_res, status_res, person_res ] = await Promise.all([
                   listDictByNameEn('certificate_type'),
					listDictByNameEn('person_status'),
					listPerson(this.searchModel)
				])
                this.certificateList = certificate_res.data.data
				this.statusList = status_res.data.data
				this.personList = person_res.data.data.list
				this.total = person_res.data.data.total
			}catch (err) {
				this.$message.error(err.data?.errorMessage || '加载数据失败')
			}
		}
	},
	created() {
		this.init()
	},
	unmounted() {
		mitt.off('openPersonAdd')
		mitt.off('openPersonEdit')
	}
}
</script>

<style scoped>
.person-list-container {
	display: flex;
	flex-direction: column;
	height: 100%;
	box-sizing: border-box;
}

.card--table {
	background-color: #fff;
	border-radius: 5px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	flex: 1;
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow: auto;
	margin-top: 0;
}

.table-col {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-height: 0;
}

.data-table {
	flex: 1;
	display: flex;
	flex-direction: column;
	height: 100% !important;
}

.pagination-container {
	display: flex;
	justify-content: flex-end;
	margin-top: 10px;
	padding: 0 20px 20px;
}

.search-flex {
	display: flex;
	align-items: center;
}

.card--search {
	margin-bottom: 20px;
	flex: none;
	height: auto;
	padding: 20px 20px;
	display: flex;
	align-items: center;
	background-color: #fff;
	border-radius: 5px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>