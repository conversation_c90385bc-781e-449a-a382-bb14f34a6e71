<template>
  <el-dialog 
    width="800px" 
    v-loading="loading" 
    destroy-on-close 
    v-model="dialog.show" 
    :title="dialog.title"
  >
    <div class="community-selector">
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-input 
          v-model="searchModel.communityName" 
          placeholder="请输入小区名称搜索" 
          clearable 
          style="width: 300px; margin-right: 16px;"
          @input="handleSearch"
        />
        <el-button type="primary" @click="search">搜索</el-button>
      </div>

      <!-- 小区列表 -->
      <div class="community-list">
        <el-table 
          :data="communityList" 
          style="width: 100%; height: 400px;" 
          @selection-change="handleSelectionChange"
          ref="communityTable"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="communityName" label="小区名称" min-width="150" />
          <el-table-column prop="address" label="地址" min-width="200" />
          <el-table-column prop="createTime" label="创建时间" width="180">
            <template #default="scope">
              {{ formatDate(scope.row.createTime) }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-area">
        <el-pagination 
          background 
          layout="prev, pager, next" 
          @current-change="currentChange"
          :total="total"
          :page-size="searchModel.pageSize"
          :current-page="searchModel.pageNum"
        />
      </div>

      <!-- 已选择的小区 -->
      <div class="selected-area" v-if="selectedCommunities.length > 0">
        <div class="selected-title">已选择的小区（{{ selectedCommunities.length }}个）：</div>
        <div class="selected-tags">
          <el-tag 
            v-for="community in selectedCommunities" 
            :key="community.id"
            closable
            @close="removeCommunity(community)"
            style="margin: 4px;"
          >
            {{ community.communityName }}
          </el-tag>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="confirmSelection" :disabled="selectedCommunities.length === 0">
          确定（{{ selectedCommunities.length }}）
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { listCommunity } from '@/api/community/community'
import { relevCommunity, getOrgCommunities } from '@/api/system/org'
import mitt from '@/utils/mitt'

export default {
  name: 'CommunitySelector',
  data() {
    return {
      loading: false,
      dialog: {
        show: false,
        title: '关联小区'
      },
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        communityName: ''
      },
      communityList: [],
      total: 0,
      selectedCommunities: [],
      currentOrgId: null
    }
  },
  methods: {
    // 搜索小区
    search() {
      this.loading = true
      listCommunity(this.searchModel)
        .then(res => {
          this.communityList = res.data.data.list
          this.total = res.data.data.total
          // 恢复选中状态
          this.restoreSelection()
        })
        .catch(err => {
          this.$message.error(err.data?.errorMessage || '获取小区列表失败')
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 处理搜索输入
    handleSearch() {
      // 防抖处理
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        this.searchModel.pageNum = 1
        this.search()
      }, 500)
    },

    // 分页变化
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      // 更新选中的小区列表
      const currentPageIds = this.communityList.map(item => item.id)
      
      // 移除当前页面取消选择的小区
      this.selectedCommunities = this.selectedCommunities.filter(item => 
        !currentPageIds.includes(item.id)
      )
      
      // 添加当前页面新选择的小区
      selection.forEach(item => {
        if (!this.selectedCommunities.find(selected => selected.id === item.id)) {
          this.selectedCommunities.push(item)
        }
      })
    },

    // 恢复选中状态
    restoreSelection() {
      this.$nextTick(() => {
        if (this.$refs.communityTable) {
          this.communityList.forEach(row => {
            const isSelected = this.selectedCommunities.find(item => item.id === row.id)
            this.$refs.communityTable.toggleRowSelection(row, !!isSelected)
          })
        }
      })
    },

    // 移除选中的小区
    removeCommunity(community) {
      this.selectedCommunities = this.selectedCommunities.filter(item => item.id !== community.id)
      // 更新表格选中状态
      this.restoreSelection()
    },

    // 确认选择
    confirmSelection() {
      if (this.selectedCommunities.length === 0) {
        this.$message.warning('请至少选择一个小区')
        return
      }

      this.loading = true
      const communityIds = this.selectedCommunities.map(item => item.id)
      
      relevCommunity({
        orgId: this.currentOrgId,
        communityIds: communityIds
      })
        .then(() => {
          this.$message.success(`成功关联 ${this.selectedCommunities.length} 个小区`)
          this.dialog.show = false
          this.$emit('success')
        })
        .catch(err => {
          this.$message.error(err.data?.errorMessage || '关联小区失败')
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 加载已关联的小区
    async loadSelectedCommunities() {
      if (!this.currentOrgId) return

      try {
        const res = await getOrgCommunities(this.currentOrgId)
        if (res.data.data && Array.isArray(res.data.data)) {
          this.selectedCommunities = res.data.data
        }
      } catch (err) {
        // 如果接口不存在或出错，不影响正常使用
        console.warn('获取已关联小区失败:', err.data?.errorMessage || err.message)
      }
    },

    // 重置数据
    resetData() {
      this.selectedCommunities = []
      this.searchModel = {
        pageNum: 1,
        pageSize: 10,
        communityName: ''
      }
      this.communityList = []
      this.total = 0
    }
  },

  mounted() {
    // 监听打开小区选择器事件
    mitt.on('openCommunitySelector', async (orgId) => {
      this.currentOrgId = orgId
      this.resetData()
      this.dialog.show = true

      // 先加载已关联的小区
      await this.loadSelectedCommunities()

      // 然后搜索小区列表
      this.search()
    })
  },

  beforeUnmount() {
    mitt.off('openCommunitySelector')
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  }
}
</script>

<style scoped>
.community-selector {
  padding: 0 8px;
}

.search-area {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.community-list {
  margin-bottom: 16px;
}

.pagination-area {
  text-align: center;
  margin-bottom: 16px;
}

.selected-area {
  border-top: 1px solid #e8e8e8;
  padding-top: 16px;
}

.selected-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.selected-tags {
  max-height: 100px;
  overflow-y: auto;
}

.dialog-footer {
  text-align: right;
}
</style>
