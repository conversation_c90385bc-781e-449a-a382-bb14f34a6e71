<template>
  <el-dialog 
    width="800px" 
    v-loading="loading" 
    destroy-on-close 
    v-model="dialog.show" 
    :title="dialog.title"
  >
    <div class="community-selector">
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-input 
          v-model="searchModel.communityName" 
          placeholder="请输入小区名称搜索" 
          clearable 
          style="width: 300px; margin-right: 16px;"
          @input="handleSearch"
        />
        <el-button type="primary" @click="search">搜索</el-button>
      </div>

      <!-- 小区列表 -->
      <div class="community-list">
        <div class="list-header">
          <span class="list-title">
            <i class="el-icon-office-building"></i>
            小区列表
          </span>
          <span class="list-count">共 {{ total }} 个小区</span>
        </div>
        <el-table
          :data="communityList"
          style="width: 100%; height: 400px;"
          @selection-change="handleSelectionChange"
          ref="communityTable"
          v-loading="loading"
          element-loading-text="加载中..."
          element-loading-spinner="el-icon-loading"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column prop="communityName" label="小区名称" min-width="150" show-overflow-tooltip>
            <template #default="scope">
              <div class="community-name">
                <i class="el-icon-office-building community-icon"></i>
                {{ scope.row.communityName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="地址" min-width="200" show-overflow-tooltip>
            <template #default="scope">
              <div class="community-address">
                <i class="el-icon-location-outline address-icon"></i>
                {{ scope.row.address || '暂无地址' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180" align="center">
            <template #default="scope">
              <div class="create-time">
                <i class="el-icon-time time-icon"></i>
                {{ formatDate(scope.row.createTime) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-area">
        <el-pagination 
          background 
          layout="prev, pager, next" 
          @current-change="currentChange"
          :total="total"
          :page-size="searchModel.pageSize"
          :current-page="searchModel.pageNum"
        />
      </div>

      <!-- 已选择的小区 -->
      <div class="selected-area" v-if="selectedCommunities.length > 0">
        <div class="selected-title">已选择的小区（{{ selectedCommunities.length }}个）：</div>
        <div class="selected-tags">
          <el-tag 
            v-for="community in selectedCommunities" 
            :key="community.id"
            closable
            @close="removeCommunity(community)"
            style="margin: 4px;"
          >
            {{ community.communityName }}
          </el-tag>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="confirmSelection" :disabled="selectedCommunities.length === 0">
          确定（{{ selectedCommunities.length }}）
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { listCommunity } from '@/api/community/community'
import { relevCommunity, getOrgCommunities } from '@/api/system/org'
import mitt from '@/utils/mitt'

export default {
  name: 'CommunitySelector',
  data() {
    return {
      loading: false,
      dialog: {
        show: false,
        title: '关联小区'
      },
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        communityName: ''
      },
      communityList: [],
      total: 0,
      selectedCommunities: [],
      currentOrgId: null
    }
  },
  methods: {
    // 搜索小区
    search() {
      this.loading = true
      listCommunity(this.searchModel)
        .then(res => {
          this.communityList = res.data.data.list
          this.total = res.data.data.total
          // 恢复选中状态
          this.restoreSelection()
        })
        .catch(err => {
          this.$message.error(err.data?.errorMessage || '获取小区列表失败')
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 处理搜索输入
    handleSearch() {
      // 防抖处理
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        this.searchModel.pageNum = 1
        this.search()
      }, 500)
    },

    // 分页变化
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      // 更新选中的小区列表
      const currentPageIds = this.communityList.map(item => item.id)
      
      // 移除当前页面取消选择的小区
      this.selectedCommunities = this.selectedCommunities.filter(item => 
        !currentPageIds.includes(item.id)
      )
      
      // 添加当前页面新选择的小区
      selection.forEach(item => {
        if (!this.selectedCommunities.find(selected => selected.id === item.id)) {
          this.selectedCommunities.push(item)
        }
      })
    },

    // 恢复选中状态
    restoreSelection() {
      this.$nextTick(() => {
        if (this.$refs.communityTable) {
          this.communityList.forEach(row => {
            const isSelected = this.selectedCommunities.find(item => item.id === row.id)
            this.$refs.communityTable.toggleRowSelection(row, !!isSelected)
          })
        }
      })
    },

    // 移除选中的小区
    removeCommunity(community) {
      this.selectedCommunities = this.selectedCommunities.filter(item => item.id !== community.id)
      // 更新表格选中状态
      this.restoreSelection()
    },

    // 确认选择
    confirmSelection() {
      if (this.selectedCommunities.length === 0) {
        this.$message.warning('请至少选择一个小区')
        return
      }

      this.loading = true
      const communityIds = this.selectedCommunities.map(item => item.id)
      
      relevCommunity({
        orgId: this.currentOrgId,
        communityIds: communityIds
      })
        .then(() => {
          this.$message.success(`成功关联 ${this.selectedCommunities.length} 个小区`)
          this.dialog.show = false
          this.$emit('success')
        })
        .catch(err => {
          this.$message.error(err.data?.errorMessage || '关联小区失败')
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 加载已关联的小区
    async loadSelectedCommunities() {
      if (!this.currentOrgId) return

      try {
        const res = await getOrgCommunities(this.currentOrgId)
        if (res.data.data && Array.isArray(res.data.data)) {
          this.selectedCommunities = res.data.data
        }
      } catch (err) {
        // 如果接口不存在或出错，不影响正常使用
        console.warn('获取已关联小区失败:', err.data?.errorMessage || err.message)
      }
    },

    // 重置数据
    resetData() {
      this.selectedCommunities = []
      this.searchModel = {
        pageNum: 1,
        pageSize: 10,
        communityName: ''
      }
      this.communityList = []
      this.total = 0
    }
  },

  mounted() {
    // 监听打开小区选择器事件
    mitt.on('openCommunitySelector', async (orgId) => {
      this.currentOrgId = orgId
      this.resetData()
      this.dialog.show = true

      // 先加载已关联的小区
      await this.loadSelectedCommunities()

      // 然后搜索小区列表
      this.search()
    })
  },

  beforeUnmount() {
    mitt.off('openCommunitySelector')
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  }
}
</script>

<style scoped>
.community-selector {
  padding: 0;
}

.search-area {
  margin-bottom: 20px;
  padding: 16px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-area .el-input {
  flex: 1;
  max-width: 300px;
}

.community-list {
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-bottom: 1px solid #e9ecef;
}

.list-title {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.list-title i {
  color: #667eea;
  font-size: 16px;
}

.list-count {
  color: #6c757d;
  font-size: 12px;
  background: #e9ecef;
  padding: 4px 8px;
  border-radius: 12px;
}

.community-list .el-table {
  border-radius: 0 0 8px 8px;
}

.community-list .el-table th {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
  border-bottom: 2px solid #e9ecef;
}

.community-list .el-table td {
  border-bottom: 1px solid #f1f3f4;
  padding: 12px 0;
}

.community-list .el-table tr:hover td {
  background-color: #f8f9fa;
}

.community-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.community-icon {
  color: #667eea;
  font-size: 14px;
}

.community-address {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6c757d;
}

.address-icon {
  color: #28a745;
  font-size: 14px;
}

.create-time {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  color: #6c757d;
  font-size: 12px;
}

.time-icon {
  color: #ffc107;
  font-size: 14px;
}

.pagination-area {
  text-align: right;
  margin-bottom: 20px;
  padding: 0 4px;
}

.selected-area {
  border-top: 2px solid #e9ecef;
  padding-top: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-top: 16px;
}

.selected-title {
  font-weight: 600;
  margin-bottom: 12px;
  color: #495057;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.selected-title::before {
  content: "✓";
  background: #28a745;
  color: white;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.selected-tags {
  max-height: 120px;
  overflow-y: auto;
  padding: 4px;
}

.selected-tags::-webkit-scrollbar {
  width: 6px;
}

.selected-tags::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.selected-tags::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.selected-tags::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.selected-tags .el-tag {
  margin: 4px 6px 4px 0;
  padding: 8px 12px;
  font-size: 13px;
  border-radius: 6px;
  background: #e3f2fd;
  border-color: #2196f3;
  color: #1976d2;
}

.selected-tags .el-tag .el-tag__close {
  color: #1976d2;
  font-weight: bold;
}

.selected-tags .el-tag .el-tag__close:hover {
  background: #1976d2;
  color: white;
}

.dialog-footer {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
  margin-top: 20px;
}

.dialog-footer .el-button {
  padding: 10px 20px;
  font-weight: 500;
}

/* 弹窗标题样式 */
:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  border-radius: 8px 8px 0 0;
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 16px;
}

:deep(.el-dialog__close) {
  color: white;
  font-size: 18px;
}

:deep(.el-dialog__close:hover) {
  color: #f0f0f0;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

/* 表格样式优化 */
:deep(.el-table .el-checkbox) {
  transform: scale(1.1);
}

:deep(.el-table .el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #2196f3;
  border-color: #2196f3;
}

/* 分页样式 */
:deep(.el-pagination) {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

:deep(.el-pagination .el-pager li) {
  border-radius: 4px;
  margin: 0 2px;
}

:deep(.el-pagination .el-pager li.active) {
  background: #2196f3;
  color: white;
}

/* 搜索按钮样式 */
.search-area .el-button--primary {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  border: none;
  padding: 10px 20px;
  font-weight: 500;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}

.search-area .el-button--primary:hover {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
}

/* 确定按钮样式 */
.dialog-footer .el-button--primary {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.dialog-footer .el-button--primary:hover {
  background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

.dialog-footer .el-button--primary:disabled {
  background: #6c757d;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-area {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .search-area .el-input {
    max-width: none;
  }

  .pagination-area {
    text-align: center;
  }

  :deep(.el-pagination) {
    justify-content: center;
  }
}
</style>
