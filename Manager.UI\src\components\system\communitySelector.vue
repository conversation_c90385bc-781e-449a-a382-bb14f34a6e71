<template>
  <el-dialog
    width="800px"
    v-loading="loading"
    destroy-on-close
    v-model="dialog.show"
    :title="dialog.title"
  >
    <div class="community-selector">
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-input
          v-model="searchModel.communityName"
          placeholder="请输入小区名称搜索"
          clearable
          @input="handleSearch"
        />
        <el-button type="primary" @click="search">搜索</el-button>
      </div>

      <!-- 小区列表 -->
      <div class="community-list">
        <el-table
          :data="communityList"
          @selection-change="handleSelectionChange"
          ref="communityTable"
          v-loading="loading"
          element-loading-text="加载中..."
          height="350"
        >
          <el-table-column type="selection" width="50" />
          <el-table-column
            prop="communityName"
            label="小区名称"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            prop="address"
            label="地址"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column prop="createTime" label="创建时间" width="150">
            <template #default="scope">
              {{ formatDate(scope.row.createTime) }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-area">
        <span class="total-info">共 {{ total }} 个小区</span>
        <el-pagination
          background
          layout="prev, pager, next"
          @current-change="currentChange"
          :total="total"
          :page-size="searchModel.pageSize"
          :current-page="searchModel.pageNum"
          small
        />
      </div>

      <!-- 已选择的小区 -->
      <div class="selected-area">
        <div class="selected-header">
          <span class="selected-title">已选择小区</span>
          <span class="selected-count"
            >{{ selectedCommunities.length }} 个</span
          >
        </div>
        <div class="selected-content" v-if="selectedCommunities.length > 0">
          <div class="selected-list">
            <div
              v-for="community in selectedCommunities"
              :key="community.communityId"
              class="selected-item"
            >
              <span class="community-name">{{ community.communityName }}</span>
              <el-button
                type="text"
                size="mini"
                @click="removeCommunity(community)"
                class="remove-btn"
              >
                <i class="el-icon-close"></i>
              </el-button>
            </div>
          </div>
        </div>
        <div v-else class="empty-selected">
          <i class="el-icon-info"></i>
          <span>暂未选择任何小区</span>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button
          type="primary"
          @click="confirmSelection"
          :disabled="selectedCommunities.length === 0"
        >
          确定（{{ selectedCommunities.length }}）
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { listCommunity } from "@/api/community/community";
import { relevCommunity, getOrgCommunities } from "@/api/system/org";
import mitt from "@/utils/mitt";

export default {
  name: "CommunitySelector",
  data() {
    return {
      loading: false,
      dialog: {
        show: false,
        title: "关联小区",
      },
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        communityName: "",
      },
      communityList: [],
      total: 0,
      selectedCommunities: [],
      currentOrgId: null,
      lastSelectedRows: [] // 存储上一次选中的行数据
    }
  },
  methods: {
    // 搜索小区
    search() {
      this.loading = true;
      listCommunity(this.searchModel)
        .then((res) => {
          this.communityList = res.data.data.list;
          this.total = res.data.data.total;
          // 恢复选中状态
          this.restoreSelection();
        })
        .catch((err) => {
          this.$message.error(err.data?.errorMessage || "获取小区列表失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 处理搜索输入
    handleSearch() {
      // 防抖处理
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.searchModel.pageNum = 1;
        this.search();
      }, 500);
    },
    // 分页大小变化
    handleSizeChange(val) {
      this.searchModel.pageSize = val;
      this.searchModel.pageNum = 1;
      this.search();
    },

    // 分页变化
    currentChange(val) {
      this.searchModel.pageNum = val;
      this.search();
    },
    // 处理选择变化
    handleSelectionChange(selectedRows) {
      // 获取当前页面的所有小区ID
      const currentPageIds = this.communityList.map(item => item.id)

      // 移除当前页面的所有小区（无论之前是否选中）
      this.selectedCommunities = this.selectedCommunities.filter(item =>
        !currentPageIds.includes(item.communityId)
      )

      // 添加当前页面新选择的小区
      selectedRows.forEach(item => {
        // 确保不重复添加
        if (!this.selectedCommunities.find(selected => selected.communityId === item.id)) {
          this.selectedCommunities.push({
            ...item,
            communityId: item.id // 将id映射为communityId
          })
        }
      })

      // 更新上次选中的行数据（用于下次比较）
      this.lastSelectedRows = [...selectedRows]
    },

    // 恢复选中状态
    restoreSelection() {
      this.$nextTick(() => {
        if (this.$refs.communityTable) {
          this.communityList.forEach(row => {
            const isSelected = this.selectedCommunities.find(item => item.communityId === row.id)
            this.$refs.communityTable.toggleRowSelection(row, !!isSelected)
          })
        }
      })
    },

    // 移除选中的小区
    removeCommunity(community) {
      this.selectedCommunities = this.selectedCommunities.filter(
        (item) => item.communityId !== community.communityId
      );
      // 更新表格选中状态
      this.restoreSelection();
    },

    // 确认选择
    confirmSelection() {
      if (this.selectedCommunities.length === 0) {
        this.$message.warning("请至少选择一个小区");
        return;
      }

      this.loading = true;
      const communityIds = this.selectedCommunities.map((item) => item.communityId);

      relevCommunity({
        orgId: this.currentOrgId,
        communityIds: communityIds,
      })
        .then(() => {
          this.$message.success(
            `成功关联 ${this.selectedCommunities.length} 个小区`
          );
          this.dialog.show = false;
          this.$emit("success");
        })
        .catch((err) => {
          this.$message.error(err.data?.errorMessage || "关联小区失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return "";
      const date = new Date(dateString);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    },

    // 加载已关联的小区
    async loadSelectedCommunities() {
      if (!this.currentOrgId) return;

      try {
        const res = await getOrgCommunities(this.currentOrgId);
        if (res.data.data && Array.isArray(res.data.data)) {
          ;
          this.selectedCommunities = res.data.data;
          console.log("已关联小区", this.selectedCommunities);
        }
      } catch (err) {
        // 如果接口不存在或出错，不影响正常使用
        console.warn(
          "获取已关联小区失败:",
          err.data?.errorMessage || err.message
        );
      }
    },

    // 重置数据
    resetData() {
      this.selectedCommunities = [];
      this.lastSelectedRows = [];
      this.searchModel = {
        pageNum: 1,
        pageSize: 10,
        communityName: "",
      };
      this.communityList = [];
      this.total = 0;
    },
  },

  mounted() {
    // 监听打开小区选择器事件
    mitt.on("openCommunitySelector", async (orgId) => {
      this.currentOrgId = orgId;
      this.resetData();
      this.dialog.show = true;

      // 先加载已关联的小区
      await this.loadSelectedCommunities();

      // 然后搜索小区列表
      this.search();
    });
  },

  beforeUnmount() {
    mitt.off("openCommunitySelector");
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  },
};
</script>

<style scoped>
.community-selector {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 80%;
  margin: 0 auto;
}

/* 搜索区域 */
.search-area {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  flex-shrink: 0;
  width: 100%;
}

.search-area .el-input {
  flex: 1;
}

/* 小区列表 */
.community-list {
  flex: 1;
  margin-bottom: 16px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
  width: 100%;
}

/* 分页区域 */
.pagination-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  margin-bottom: 16px;
  flex-shrink: 0;
  width: 100%;
}

.total-info {
  color: #909399;
  font-size: 13px;
}

/* 已选择区域 */
.selected-area {
  border-top: 1px solid #dcdfe6;
  padding: 16px;
  flex-shrink: 0;
  max-height: 150px;
  overflow: hidden;
  background: #f8f9fa;
  border-radius: 6px;
  width: 100%;
}

.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.selected-title {
  font-weight: 600;
  color: #606266;
  font-size: 14px;
}

.selected-count {
  background: #409eff;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.selected-content {
  max-height: 100px;
  overflow-y: auto;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-item {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 6px 8px;
  font-size: 13px;
  transition: all 0.3s ease;
}

.selected-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

.community-name {
  color: #606266;
  margin-right: 6px;
}

.remove-btn {
  color: #909399;
  padding: 0;
  margin: 0;
  min-height: auto;
  font-size: 12px;
}

.remove-btn:hover {
  color: #f56c6c;
}

.empty-selected {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 13px;
  padding: 20px;
}

.empty-selected i {
  margin-right: 6px;
  font-size: 16px;
}

/* 弹窗底部 */
.dialog-footer {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #dcdfe6;
  margin-top: 16px;
}

/* 弹窗样式 */
:deep(.el-dialog__header) {
  background: #409eff;
  color: white;
  padding: 16px 20px;
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 500;
}

:deep(.el-dialog__close) {
  color: white;
}

:deep(.el-dialog__body) {
  padding: 24px;
  height: 600px;
  display: flex;
  flex-direction: column;
}

/* 表格样式 */
:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

:deep(.el-table td) {
  padding: 8px 0;
}

/* 表格样式优化 */
:deep(.el-table .el-checkbox) {
  transform: scale(1.1);
}

:deep(.el-table .el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #2196f3;
  border-color: #2196f3;
}

/* 分页样式 */
:deep(.el-pagination) {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

:deep(.el-pagination .el-pager li) {
  border-radius: 4px;
  margin: 0 2px;
}

:deep(.el-pagination .el-pager li.active) {
  background: #2196f3;
  color: white;
}

/* 搜索按钮样式 */
.search-area .el-button--primary {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  border: none;
  padding: 10px 20px;
  font-weight: 500;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}

.search-area .el-button--primary:hover {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
}

/* 确定按钮样式 */
.dialog-footer .el-button--primary {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.dialog-footer .el-button--primary:hover {
  background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

.dialog-footer .el-button--primary:disabled {
  background: #6c757d;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-area {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .search-area .el-input {
    max-width: none;
  }

  .pagination-area {
    text-align: center;
  }

  :deep(.el-pagination) {
    justify-content: center;
  }
}
</style>
